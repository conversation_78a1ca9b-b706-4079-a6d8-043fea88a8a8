<?php
/**
 * Post Type Registration for Post List Box
 */

class PPS_Post_List_Box_Post_Type {
    
    const POST_TYPE_BOXES = 'pps_post_list_box';
    
    /**
     * Initialize post type registration
     */
    public static function init() {
        add_action('init', [__CLASS__, 'register_post_type']);
    }
    
    /**
     * Register the post types.
     */
    public static function register_post_type()
    {
        $labelSingular = __('Post List Box', 'publishpress-series-pro');
        $labelPlural = __('Post List Boxes', 'publishpress-series-pro');

        $postTypeLabels = [
            'name' => _x('%2$s', 'Post List Box post type name', 'publishpress-series-pro'),
            'singular_name' => _x(
                '%1$s',
                'singular post list box post type name',
                'publishpress-series-pro'
            ),
            'add_new' => __('New %1s', 'publishpress-series-pro'),
            'add_new_item' => __('Add New %1$s', 'publishpress-series-pro'),
            'edit_item' => __('Edit %1$s', 'publishpress-series-pro'),
            'new_item' => __('New %1$s', 'publishpress-series-pro'),
            'all_items' => __('%2$s', 'publishpress-series-pro'),
            'view_item' => __('View %1$s', 'publishpress-series-pro'),
            'search_items' => __('Search %2$s', 'publishpress-series-pro'),
            'not_found' => __('No %2$s found', 'publishpress-series-pro'),
            'not_found_in_trash' => __('No %2$s found in Trash', 'publishpress-series-pro'),
            'parent_item_colon' => '',
            'menu_name' => _x('%2$s', 'custom layout post type menu name', 'publishpress-series-pro'),
            'featured_image' => __('%1$s Image', 'publishpress-series-pro'),
            'set_featured_image' => __('Set %1$s Image', 'publishpress-series-pro'),
            'remove_featured_image' => __('Remove %1$s Image', 'publishpress-series-pro'),
            'use_featured_image' => __('Use as %1$s Image', 'publishpress-series-pro'),
            'filter_items_list' => __('Filter %2$s list', 'publishpress-series-pro'),
            'items_list_navigation' => __('%2$s list navigation', 'publishpress-series-pro'),
            'items_list' => __('%2$s list', 'publishpress-series-pro'),
        ];

        foreach ($postTypeLabels as $labelKey => $labelValue) {
            $postTypeLabels[$labelKey] = sprintf($labelValue, $labelSingular, $labelPlural);
        }

        $postTypeArgs = [
            'labels' => $postTypeLabels,
            'public' => false,
            'publicly_queryable' => false,
            'show_ui' => true,
            'show_in_menu' => false,
            'map_meta_cap' => true,
            'has_archive' => false,
            'hierarchical' => false,
            'rewrite' => false,
            'supports' => ['title'],
        ];
        register_post_type(self::POST_TYPE_BOXES, $postTypeArgs);
    }
}