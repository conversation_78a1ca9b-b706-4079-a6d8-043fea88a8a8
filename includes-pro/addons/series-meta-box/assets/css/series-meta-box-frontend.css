/* Frontend styles for Series Meta Boxes */

.pps-series-meta-box {
    position: relative;
    display: block;
    margin: 1.5em 0;
    padding: 1.25em 1.5em;
    background-color: #eef5ff;
    color: #1d2327;
    border: 1px solid #c7d7f5;
    border-radius: 6px;
}

.pps-series-meta-box a {
    color: inherit;
    text-decoration: none;
}

.pps-series-meta-box a:hover,
.pps-series-meta-box a:focus {
    text-decoration: underline;
}

.pps-series-meta-box .pps-series-meta-postcontent {
    margin-top: 1em;
}

.pps-series-meta-variant-card {
    background-color: #1a5aff;
    color: #ffffff;
    border: none;
}

.pps-series-meta-variant-card a {
    color: #ffe08a;
}

.pps-series-meta-variant-minimal {
    background-color: #f7f7f7;
    border-color: #dfdfdf;
    color: #1d2327;
}

.pps-series-meta-variant-compact {
    display: inline-flex;
    align-items: center;
    gap: 0.75em;
    padding: 0.75em 1em;
}

.pps-series-meta-box .pps-series-meta-icon {
    font-size: 1.25em;
    margin-right: 0.5em;
}

@media (max-width: 600px) {
    .pps-series-meta-box {
        padding: 1em;
    }

    .pps-series-meta-variant-compact {
        flex-direction: column;
        align-items: flex-start;
    }
}
