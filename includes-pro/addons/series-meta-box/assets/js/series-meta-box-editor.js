jQuery(document).ready(function ($) {
    
    function ppsSeriesMetaBoxEditorInit() {
        if (typeof ppsSeriesMetaBoxEditor === 'undefined') {
            return;
        }

        // Tab switching functionality
        $('.pps-series-meta-box-editor-tabs a').on('click', function (e) {
            e.preventDefault();

            var tab = $(this).data('tab');

            $('.pps-series-meta-box-editor-tabs a').removeClass('active');
            $(this).addClass('active');

            $('.pps-series-meta-box-editor table tbody tr').hide();
            $('.pps-series-meta-box-editor table tbody tr[data-tab="' + tab + '"]').show();
        });

        // Color picker initialization
        $('.pps-color-picker').wpColorPicker({
            change: function(event, ui) {
                debouncedUpdate();
            },
            clear: function() {
                updatePreview();
            }
        });

        // Preview functionality
        function updatePreview() {
            var postId = ppsSeriesMetaBoxEditor.post_id;
            var formData = $('#post').serialize();
            
            $('.pps-series-meta-box-preview').addClass('loading');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'pps_update_series_meta_box_preview',
                    post_id: postId,
                    settings: formData,
                    nonce: ppsSeriesMetaBoxEditor.nonce
                },
                success: function (response) {
                    if (response && response.success && response.data && response.data.preview) {
                        $('#pps-series-meta-preview-content').html(response.data.preview);
                    }
                    $('.pps-series-meta-box-preview').removeClass('loading');
                },
                error: function () {
                    $('.pps-series-meta-box-preview').removeClass('loading');
                    $('#pps-series-meta-preview-content').html('<div class="notice notice-error"><p>Failed to update preview. Please try again.</p></div>');
                }
            });
        }

        // Update preview when form fields change (with debounce)
        var previewTimeout;
        function debouncedUpdate() {
            clearTimeout(previewTimeout);
            previewTimeout = setTimeout(updatePreview, 300);
        }

        // Delegate to capture dynamically added inputs
        $(document)
            .on('change input keyup', '.pps-series-meta-box-editor-fields input, .pps-series-meta-box-editor-fields select, .pps-series-meta-box-editor-fields textarea', debouncedUpdate)
            .on('change input keyup', '.pps-series-meta-boxes-editor-table input, .pps-series-meta-boxes-editor-table select, .pps-series-meta-boxes-editor-table textarea', debouncedUpdate)
            .on('change input keyup', '.publishpress-series-meta-box-editor :input', debouncedUpdate);

       

        // Form validation
        $('form#post').on('submit', function (e) {
            var title = $('#title').val();
            if (!title || title.trim() === '') {
                alert('Please enter a title for the Series Meta Box.');
                e.preventDefault();
                return false;
            }
        });
    }

    // Initialize the editor
    ppsSeriesMetaBoxEditorInit();
    
    // Trigger an initial preview
    jQuery(function(){
        if (typeof ppsSeriesMetaBoxEditor !== 'undefined') {
            setTimeout(function(){
                jQuery(document).trigger('series-meta-box-editor-reinit');
            }, 50);
        }
    });

    // Re-initialize when needed
    $(document).on('series-meta-box-editor-reinit', ppsSeriesMetaBoxEditorInit);
});
