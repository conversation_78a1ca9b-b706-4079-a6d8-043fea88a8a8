<?php
/**
 * Renderer utilities for Series Meta Boxes.
 */

if (! defined('ABSPATH')) {
    exit;
}

require_once __DIR__ . '/../includes/class-utilities.php';

class SeriesMetaBoxRenderer
{
    /**
     * Store dynamic CSS snippets keyed by layout ID.
     *
     * @var array
     */
    private static $dynamic_css = [];

    /**
     * Flag to avoid duplicate asset loads.
     *
     * @var bool
     */
    private static $assets_enqueued = false;

    /**
     * Boot front-end integration.
     */
    public static function init()
    {
        add_shortcode('pps_meta_box', [__CLASS__, 'render_shortcode']);
        add_action('wp_enqueue_scripts', [__CLASS__, 'enqueue_frontend_assets']);
        add_action('wp_footer', [__CLASS__, 'output_dynamic_css']);
    }

    /**
     * Enqueue shared front-end styles.
     */
    public static function enqueue_frontend_assets()
    {
        if (self::$assets_enqueued) {
            return;
        }

        $base_file = PPS_Series_Meta_Box_Utilities::get_module_path('series-meta-box.php');
        $style_url = plugins_url('assets/css/series-meta-box-frontend.css', $base_file);
        $style_file = PPS_Series_Meta_Box_Utilities::get_module_path('assets/css/series-meta-box-frontend.css');

        $version = ORG_SERIES_VERSION;
        if ($style_file && file_exists($style_file)) {
            $filetime = filemtime($style_file);
            if ($filetime) {
                $version .= '-' . $filetime;
            }
        }

        wp_enqueue_style('pps-series-meta-box-frontend', $style_url, [], $version);

        self::$assets_enqueued = true;
    }

    /**
     * Render the shortcode `[pps_meta_box layout="..."]`.
     *
     * @param array $atts Shortcode attributes.
     *
     * @return string
     */
    public static function render_shortcode($atts)
    {
        $atts = shortcode_atts([
            'layout'  => '',
            'series'  => '',
            'post_id' => 0,
        ], $atts, 'pps_meta_box');

        if (empty($atts['layout'])) {
            return '<!-- Series Meta Box: layout attribute missing -->';
        }

        $layout_id = self::normalize_layout_id($atts['layout']);
        if (! $layout_id || 'publish' !== get_post_status($layout_id)) {
            return '<!-- Series Meta Box: invalid or unpublished layout -->';
        }

        $post = null;
        if (! empty($atts['post_id'])) {
            $post = get_post((int) $atts['post_id']);
        } elseif (is_singular()) {
            $post = get_queried_object();
        }

        $series_term = self::resolve_series_term($atts['series'], $post);
        if (! $series_term) {
            return '<!-- Series Meta Box: unable to determine series -->';
        }

        $context = [
            'series_term' => $series_term,
            'post'        => $post,
            'context'     => 'shortcode',
        ];

        return self::render_layout_for_series($layout_id, $context, false);
    }

    /**
     * Render a layout for automatic meta injection.
     *
     * @param int   $layout_id Layout post ID.
     * @param array $context   Context array with `series_term`, `post` and optional flags.
     * @param bool  $for_excerpt Whether to use the excerpt template.
     *
     * @return string
     */
    public static function render_layout_for_series($layout_id, array $context, $for_excerpt)
    {
        $settings = PPS_Series_Meta_Box_Utilities::get_meta_box_settings($layout_id);
        if (empty($settings)) {
            return '';
        }

        $series_term = isset($context['series_term']) ? $context['series_term'] : null;
        $post        = isset($context['post']) ? $context['post'] : null;

        if (! $series_term || ! isset($series_term->term_id)) {
            return '';
        }

        $series_id = (int) $series_term->term_id;
        $post_id   = ($for_excerpt && $post instanceof WP_Post) ? (int) $post->ID : 0;

        $template_key = $for_excerpt ? 'meta_excerpt_template' : 'meta_template';
        $template     = isset($settings[$template_key]) ? $settings[$template_key] : '';

        if ('' === trim($template)) {
            return '';
        }

        $render_context = [
            'series_term' => $series_term,
            'post'        => $post,
            'series_id'   => $series_id,
            'post_id'     => $post_id,
            'excerpt'     => $for_excerpt,
        ];

        $content = self::render_template_with_tokens($template, $render_context);
        if ('' === $content) {
            return '';
        }

        if (false === strpos($content, '%postcontent%')) {
            $content .= '<div class="pps-series-meta-postcontent">%postcontent%</div>';
        }

        $layout_class = 'pps-series-meta-box-' . $layout_id;
        self::capture_dynamic_css($layout_class, $settings);

        $variant = isset($settings['layout_variant']) ? sanitize_html_class($settings['layout_variant']) : 'classic';
        $wrapper_classes = [
            'pps-series-meta-box',
            'pps-series-meta-variant-' . $variant,
            $layout_class,
        ];

        if ($for_excerpt) {
            $wrapper_classes[] = 'pps-series-meta-excerpt';
        }

        $wrapper_attrs = [
            'class' => implode(' ', array_filter($wrapper_classes)),
            'data-series-id' => $series_id,
        ];

        $attributes = '';
        foreach ($wrapper_attrs as $name => $value) {
            $attributes .= sprintf(' %s="%s"', esc_attr($name), esc_attr($value));
        }

        $output = sprintf('<div%s>%s</div>', $attributes, $content);

        /**
         * Filter the rendered Series Meta Box output.
         *
         * @param string $output    Rendered HTML.
         * @param int    $layout_id Layout post ID.
         * @param array  $settings  Layout settings array.
         * @param array  $context   Rendering context.
         * @param bool   $for_excerpt Whether rendering for excerpt.
         */
        return apply_filters('pps_series_meta_box_render_layout', $output, $layout_id, $settings, $context, $for_excerpt);
    }

    /**
     * Render preview markup from raw settings.
     *
     * @param array $settings Editor settings array.
     * @param array $context  Context array from preview handler.
     *
     * @return string
     */
    public static function render_from_settings(array $settings, array $context = [])
    {
        $series_term = isset($context['series_term']) ? $context['series_term'] : null;
        $post        = isset($context['post']) ? $context['post'] : null;
        $total_posts = isset($context['total_posts']) ? (int) $context['total_posts'] : 3;
        $series_part = isset($context['series_part']) ? (int) $context['series_part'] : 1;
        $variant     = isset($settings['layout_variant']) ? sanitize_html_class($settings['layout_variant']) : 'classic';

        $layout_class = 'pps-series-meta-box-preview';
        self::capture_dynamic_css($layout_class, $settings);

        $tokens = self::build_preview_tokens($series_term, $post, $series_part, $total_posts);
        $template = isset($settings['meta_template']) ? $settings['meta_template'] : '';

        $content = strtr($template, $tokens);
        if (false === strpos($content, '%postcontent%')) {
            $content .= '<div class="pps-series-meta-postcontent-placeholder">%postcontent%</div>';
        }

        $wrapper_classes = [
            'pps-series-meta-box',
            'pps-series-meta-preview',
            'pps-series-meta-variant-' . $variant,
            $layout_class,
        ];

        $attributes = sprintf(' class="%s"', esc_attr(implode(' ', $wrapper_classes)));

        return sprintf('<div%s>%s</div>', $attributes, $content);
    }

    /**
     * Generate content via token_replace when available.
     *
     * @param string $template Template string.
     * @param array  $context  Context array.
     *
     * @return string
     */
    private static function render_template_with_tokens($template, array $context)
    {
        $series_id = isset($context['series_id']) ? (int) $context['series_id'] : 0;
        $post_id   = isset($context['post_id']) ? (int) $context['post_id'] : 0;

        if (function_exists('token_replace')) {
            // token_replace expects slashes stripped.
            $template = stripslashes($template);
            return token_replace($template, 'other', $post_id, $series_id);
        }

        return stripslashes($template);
    }

    /**
     * Prepare preview tokens for sample output.
     *
     * @param WP_Term|null $series_term Series term sample.
     * @param WP_Post|object|null $post Sample post.
     * @param int $series_part Current part sample.
     * @param int $total_posts Total posts sample.
     *
     * @return array
     */
    private static function build_preview_tokens($series_term, $post, $series_part, $total_posts)
    {
        $series_name = $series_term ? $series_term->name : __('Sample Series', 'publishpress-series-pro');
        $series_link = '<a href="#">' . esc_html($series_name) . '</a>';

        $post_title = $post && isset($post->post_title) ? $post->post_title : __('Sample Post Title', 'publishpress-series-pro');
        $post_link  = '<a href="#">' . esc_html($post_title) . '</a>';
        $post_excerpt = $post && isset($post->post_excerpt) ? $post->post_excerpt : __('Lorem ipsum dolor sit amet, consectetur adipiscing elit.', 'publishpress-series-pro');

        return [
            '%series_title%'                => esc_html($series_name),
            '%series_title_linked%'         => $series_link,
            '%series_icon%'                 => '<span class="pps-series-meta-icon">★</span>',
            '%series_icon_linked%'          => '<span class="pps-series-meta-icon">★</span>',
            '%post_title%'                  => esc_html($post_title),
            '%post_title_linked%'           => $post_link,
            '%post_excerpt%'                => esc_html($post_excerpt),
            '%postcontent%'                 => '%postcontent%',
            '%series_part%'                 => (string) $series_part,
            '%total_posts_in_series%'       => (string) $total_posts,
            '%previous_post%'               => '<span class="pps-series-meta-nav">' . esc_html__('Previous post link', 'publishpress-series-pro') . '</span>',
            '%next_post%'                   => '<span class="pps-series-meta-nav">' . esc_html__('Next post link', 'publishpress-series-pro') . '</span>',
            '%series_description%'          => esc_html__('Sample series description.', 'publishpress-series-pro'),
            '%series_post_list%'            => '',
            '%series_table_of_contents%'    => '',
            '%list_of_series_posts%'        => '',
            '%series_post_navigation%'      => '',
        ];
    }

    /**
     * Capture dynamic CSS for a specific layout class.
     *
     * @param string $layout_class CSS class name.
     * @param array  $settings     Layout settings.
     */
    private static function capture_dynamic_css($layout_class, array $settings)
    {
        if (isset(self::$dynamic_css[$layout_class])) {
            return;
        }

        $parts = [];

        $padding_v = isset($settings['padding_vertical']) ? (int) $settings['padding_vertical'] : 0;
        $padding_h = isset($settings['padding_horizontal']) ? (int) $settings['padding_horizontal'] : 0;
        if ($padding_v || $padding_h) {
            $parts[] = sprintf('padding: %dpx %dpx;', $padding_v, $padding_h);
        }

        if (! empty($settings['background_color'])) {
            $parts[] = 'background-color: ' . esc_attr($settings['background_color']) . ';';
        }

        if (! empty($settings['text_color'])) {
            $parts[] = 'color: ' . esc_attr($settings['text_color']) . ';';
        }

        $border_width = isset($settings['border_width']) ? (int) $settings['border_width'] : 0;
        if ($border_width > 0) {
            $border_color = ! empty($settings['border_color']) ? $settings['border_color'] : '#d8dee9';
            $parts[] = sprintf('border: %dpx solid %s;', $border_width, esc_attr($border_color));
        }

        if (! empty($settings['border_radius'])) {
            $parts[] = 'border-radius: ' . (int) $settings['border_radius'] . 'px;';
        }

        $css = [];
        if (! empty($parts)) {
            $css[] = sprintf('.%1$s { %2$s }', esc_attr($layout_class), implode(' ', $parts));
        }

        if (! empty($settings['link_color'])) {
            $link_color = esc_attr($settings['link_color']);
            $css[] = sprintf('.%1$s a, .%1$s a:visited { color: %2$s; }', esc_attr($layout_class), $link_color);
        }

        if (! empty($settings['custom_css'])) {
            $custom = str_replace('.seriesmeta', '.' . $layout_class, $settings['custom_css']);
            $css[] = trim($custom);
        }

        if (! empty($css)) {
            self::$dynamic_css[$layout_class] = implode("\n", $css);
        }
    }

    /**
     * Output collected dynamic CSS in the footer.
     */
    public static function output_dynamic_css()
    {
        if (empty(self::$dynamic_css)) {
            return;
        }

        echo '<style type="text/css" id="pps-series-meta-box-dynamic-css">' . implode("\n", self::$dynamic_css) . '</style>';
    }

    /**
     * Normalize layout attribute to a post ID.
     *
     * @param string $raw Raw attribute value.
     *
     * @return int
     */
    public static function normalize_layout_id($raw)
    {
        if (is_numeric($raw)) {
            return (int) $raw;
        }

        if (is_string($raw) && 0 === strpos($raw, 'pps_meta_box_')) {
            return (int) str_replace('pps_meta_box_', '', $raw);
        }

        return 0;
    }

    /**
     * Determine the series term to render for.
     *
     * @param string $series_attr Series attribute value.
     * @param WP_Post|null $post Post context.
     *
     * @return WP_Term|false
     */
    public static function resolve_series_term($series_attr, $post)
    {
        if (! empty($series_attr)) {
            if (is_numeric($series_attr)) {
                $term = get_term((int) $series_attr, 'series');
                if ($term && ! is_wp_error($term)) {
                    return $term;
                }
            } else {
                $term = get_term_by('slug', $series_attr, 'series');
                if ($term && ! is_wp_error($term)) {
                    return $term;
                }
            }
            return false;
        }

        if ($post instanceof WP_Post) {
            $terms = get_the_terms($post->ID, 'series');
            if (! empty($terms) && ! is_wp_error($terms)) {
                return reset($terms);
            }
        }

        if (is_tax('series')) {
            $term = get_queried_object();
            if ($term && isset($term->term_id)) {
                return $term;
            }
        }

        return false;
    }
}
