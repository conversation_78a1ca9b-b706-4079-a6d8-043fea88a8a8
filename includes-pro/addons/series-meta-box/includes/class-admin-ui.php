<?php
/**
 * Admin UI for Series Meta Boxes
 */

if (! defined('ABSPATH')) {
    exit;
}

class PPS_Series_Meta_Box_Admin_UI
{
    /**
     * Boot hooks
     */
    public static function init()
    {
        add_action('admin_menu', [__CLASS__, 'register_menu'], 60);
        add_filter('parent_file', [__CLASS__, 'highlight_menu']);
        add_action('add_meta_boxes', [__CLASS__, 'add_meta_boxes']);
        add_filter('manage_edit-' . PPS_Series_Meta_Box_Utilities::POST_TYPE . '_columns', [__CLASS__, 'register_columns']);
        add_action('manage_' . PPS_Series_Meta_Box_Utilities::POST_TYPE . '_posts_custom_column', [__CLASS__, 'render_columns'], 10, 2);
    }

    /**
     * Add submenu entry under Series options
     */
    public static function register_menu()
    {
        add_submenu_page(
            'orgseries_options_page',
            __('Series Meta Boxes', 'publishpress-series-pro'),
            __('Series Meta Boxes', 'publishpress-series-pro'),
            'manage_publishpress_series',
            'edit.php?post_type=' . PPS_Series_Meta_Box_Utilities::POST_TYPE
        );
    }

    /**
     * Ensure proper menu highlighting when editing
     */
    public static function highlight_menu($parent_file)
    {
        global $current_screen;

        if (! empty($current_screen->post_type) && $current_screen->post_type === PPS_Series_Meta_Box_Utilities::POST_TYPE) {
            $parent_file = 'orgseries_options_page';
        }

        return $parent_file;
    }

    /**
     * Setup meta boxes
     */
    public static function add_meta_boxes()
    {
        add_meta_box(
            'pps_series_meta_box_preview',
            __('Series Meta Box Preview', 'publishpress-series-pro'),
            [__CLASS__, 'render_preview_box'],
            PPS_Series_Meta_Box_Utilities::POST_TYPE,
            'normal',
            'high'
        );

        add_meta_box(
            'pps_series_meta_box_editor',
            __('Series Meta Box Editor', 'publishpress-series-pro'),
            [__CLASS__, 'render_editor_box'],
            PPS_Series_Meta_Box_Utilities::POST_TYPE,
            'normal',
            'high'
        );

        add_meta_box(
            'pps_series_meta_box_shortcode',
            __('Shortcode', 'publishpress-series-pro'),
            [__CLASS__, 'render_shortcode_box'],
            PPS_Series_Meta_Box_Utilities::POST_TYPE,
            'side',
            'default'
        );
    }

    /**
     * Preview metabox content
     */
    public static function render_preview_box(WP_Post $post)
    {
        echo '<div class="pps-series-meta-preview">';
        echo '<div id="pps-series-meta-preview-content" class="pps-series-meta-preview-content">';
        PPS_Series_Meta_Box_Preview::render_preview($post);
        echo '</div>';
        echo '</div>';
    }

    /**
     * Editor metabox content
     */
    public static function render_editor_box(WP_Post $post)
    {
        $tabs = apply_filters('pps_series_meta_box_editor_tabs', [], $post);
        $fields = PPS_Series_Meta_Box_Fields::get_fields($post);
        $settings = PPS_Series_Meta_Box_Utilities::get_meta_box_settings($post->ID, $post->post_status === 'auto-draft');

        echo '<div class="pressshack-admin-wrapper publishpress-series-meta-box-editor">';

        if (! empty($tabs)) {
            echo '<div class="pps-series-meta-box-editor-tabs"><ul>';
            foreach ($tabs as $key => $data) {
                $active = $key === PPS_Series_Meta_Box_Fields::DEFAULT_TAB ? ' active' : '';
                echo '<li><a href="#" data-tab="' . esc_attr($key) . '"' . $active . '>';
                if (! empty($data['icon'])) {
                    echo '<span class="dashicons ' . esc_attr($data['icon']) . '"></span> ';
                }
                echo esc_html($data['label']);
                echo '</a></li>';
            }
            echo '</ul></div>';
        }

        echo '<div class="pps-series-meta-box-editor-fields wrapper-column">';
        echo '<table class="form-table pps-series-meta-boxes-editor-table fixed" role="presentation"><tbody>';
        foreach ($fields as $key => $field) {
            $value = isset($settings[$key]) ? $settings[$key] : '';
            $field['key'] = $key;
            $field['value'] = $value;
            self::render_field_row($field);
        }
        echo '</tbody></table>';

        wp_nonce_field(PPS_SERIES_META_BOX_NONCE, PPS_SERIES_META_BOX_NONCE_FIELD);

        echo '</div>'; // .pps-series-meta-box-editor-fields
        echo '</div>'; // .publishpress-series-meta-box-editor
    }

    /**
     * Render shortcode info
     */
    public static function render_shortcode_box(WP_Post $post)
    {
        $layout_slug = 'pps_meta_box_' . $post->ID;
        echo '<p><label for="pps-series-meta-shortcode">' . esc_html__('Use this shortcode:', 'publishpress-series-pro') . '</label></p>';
        echo '<textarea id="pps-series-meta-shortcode" readonly class="widefat" rows="2">[pps_meta_box layout="' . esc_attr($layout_slug) . '"]</textarea>';
        echo '<p class="description">' . esc_html__('Insert into posts or pages to display this Series Meta Box manually.', 'publishpress-series-pro') . '</p>';
    }

    /**
     * Add custom columns
     */
    public static function register_columns($columns)
    {
        $columns['series_meta_default'] = esc_html__('Default Meta Box', 'publishpress-series-pro');
        $columns['series_meta_shortcode'] = esc_html__('Shortcode', 'publishpress-series-pro');

        return $columns;
    }

    /**
     * Render column content
     */
    public static function render_columns($column, $post_id)
    {
        if ('series_meta_shortcode' === $column) {
            $layout_slug = 'pps_meta_box_' . $post_id;
            echo '<code>[pps_meta_box layout="' . esc_html($layout_slug) . '"]</code>';
            return;
        }

        if ('series_meta_default' === $column) {
            $options = get_option('org_series_options');
            $selected = isset($options['series_meta_box_selection']) ? (int) $options['series_meta_box_selection'] : 0;
            if ($selected === (int) $post_id) {
                echo '<span class="dashicons dashicons-yes" style="color:green;"></span>';
            }
        }
    }

    /**
     * Render individual field rows
     */
    private static function render_field_row(array $args)
    {
        $defaults = [
            'type'        => 'text',
            'tab'         => PPS_Series_Meta_Box_Fields::DEFAULT_TAB,
            'label'       => '',
            'description' => '',
            'rows'        => 5,
            'options'     => [],
            'value'       => '',
        ];

        $args = array_merge($defaults, $args);
        $tab_attr = ' data-tab="' . esc_attr($args['tab']) . '"';
        $row_style = $args['tab'] === PPS_Series_Meta_Box_Fields::DEFAULT_TAB ? '' : ' style="display:none;"';

        echo '<tr' . $tab_attr . $row_style . '>';

        echo '<th scope="row">';
        if (! empty($args['label'])) {
            echo '<label for="' . esc_attr($args['key']) . '">' . esc_html($args['label']) . '</label>';
        }
        echo '</th>';

        echo '<td class="pps-field">';
        self::render_field_input($args);
        if (! empty($args['description'])) {
            echo '<p class="description">' . wp_kses_post($args['description']) . '</p>';
        }
        echo '</td>';

        echo '</tr>';
    }

    /**
     * Render different input types
     */
    private static function render_field_input(array $args)
    {
        $key = esc_attr($args['key']);
        $value = $args['value'];

        switch ($args['type']) {
            case 'textarea':
                printf(
                    '<textarea id="%1$s" name="%1$s" rows="%2$d" class="widefat">%3$s</textarea>',
                    $key,
                    (int) $args['rows'],
                    esc_textarea($value)
                );
                break;

            case 'checkbox':
                printf(
                    '<label><input type="checkbox" id="%1$s" name="%1$s" value="1" %2$s /> %3$s</label>',
                    $key,
                    checked(1, (int) $value, false),
                    ''
                );
                break;

            case 'select':
                echo '<select id="' . $key . '" name="' . $key . '">';
                foreach ($args['options'] as $option_value => $label) {
                    printf(
                        '<option value="%1$s" %2$s>%3$s</option>',
                        esc_attr($option_value),
                        selected($option_value, $value, false),
                        esc_html($label)
                    );
                }
                echo '</select>';
                break;

            case 'color':
                printf(
                    '<input type="text" id="%1$s" name="%1$s" value="%2$s" class="pps-color-picker" />',
                    $key,
                    esc_attr($value)
                );
                break;

            case 'number':
                printf(
                    '<input type="number" id="%1$s" name="%1$s" value="%2$s" class="small-text" />',
                    $key,
                    esc_attr($value)
                );
                break;

            default:
                printf(
                    '<input type="text" id="%1$s" name="%1$s" value="%2$s" class="regular-text" />',
                    $key,
                    esc_attr($value)
                );
                break;
        }
    }
}
