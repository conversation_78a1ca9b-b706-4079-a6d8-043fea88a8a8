<?php
/**
 * AJAX handlers for Series Meta Box editor
 */

if (! defined('ABSPATH')) {
    exit;
}

class PPS_Series_Meta_Box_Ajax
{
    /**
     * Boot hooks
     */
    public static function init()
    {
        add_action('wp_ajax_pps_update_series_meta_box_preview', [__CLASS__, 'update_preview']);
        add_action('wp_ajax_pps_export_series_meta_box', [__CLASS__, 'export_layout']);
        add_action('wp_ajax_pps_import_series_meta_box', [__CLASS__, 'import_layout']);
        add_action('wp_ajax_pps_reset_series_meta_box', [__CLASS__, 'reset_layout']);
    }

    /**
     * Build preview output
     */
    public static function update_preview()
    {
        check_ajax_referer('series-meta-box-nonce', 'nonce');

        $post_id = isset($_POST['post_id']) ? (int) $_POST['post_id'] : 0;
        if (! $post_id) {
            wp_send_json_error(['message' => __('Invalid post ID.', 'publishpress-series-pro')]);
        }

        $form_data = isset($_POST['settings']) ? wp_unslash($_POST['settings']) : '';
        $settings = [];

        if ($form_data) {
            parse_str($form_data, $settings);
        }

        $settings = array_merge(
            PPS_Series_Meta_Box_Utilities::get_meta_box_settings($post_id),
            array_intersect_key($settings, PPS_Series_Meta_Box_Utilities::get_meta_box_settings($post_id))
        );

        $series_term = PPS_Series_Meta_Box_Utilities::ensure_sample_series_term();
        if (! $series_term) {
            wp_send_json_error(['message' => __('No series available to preview.', 'publishpress-series-pro')]);
        }

        $posts = PPS_Series_Meta_Box_Utilities::get_sample_series_posts($series_term->term_id);
        $current_post = $posts ? $posts[0] : null;

        ob_start();
        echo SeriesMetaBoxRenderer::render_from_settings(
            $settings,
            [
                'series_term' => $series_term,
                'post'        => $current_post,
                'total_posts' => count($posts),
                'series_part' => 1,
                'context'     => 'preview',
            ]
        );
        SeriesMetaBoxRenderer::output_dynamic_css();
        $preview = ob_get_clean();

        wp_send_json_success(['preview' => $preview]);
    }

    /**
     * Export settings JSON
     */
    public static function export_layout()
    {
        check_ajax_referer('series-meta-box-nonce', 'nonce');

        $post_id = isset($_POST['post_id']) ? (int) $_POST['post_id'] : 0;
        if (! $post_id) {
            wp_send_json_error(['message' => __('Invalid post ID.', 'publishpress-series-pro')]);
        }

        $settings = PPS_Series_Meta_Box_Utilities::get_meta_box_settings($post_id);
        $post      = get_post($post_id);

        wp_send_json_success([
            'settings' => $settings,
            'slug'     => $post ? $post->post_name : '',
        ]);
    }

    /**
     * Import settings
     */
    public static function import_layout()
    {
        check_ajax_referer('series-meta-box-nonce', 'nonce');

        $post_id = isset($_POST['post_id']) ? (int) $_POST['post_id'] : 0;
        $settings = isset($_POST['settings']) && is_array($_POST['settings']) ? $_POST['settings'] : [];

        if (! $post_id || empty($settings)) {
            wp_send_json_error(['message' => __('Invalid import data.', 'publishpress-series-pro')]);
        }

        update_post_meta($post_id, PPS_Series_Meta_Box_Utilities::META_PREFIX . 'layout_meta_value', $settings);

        wp_send_json_success(['message' => __('Settings imported successfully.', 'publishpress-series-pro')]);
    }

    /**
     * Reset layout to defaults
     */
    public static function reset_layout()
    {
        check_ajax_referer('series-meta-box-nonce', 'nonce');

        $post_id = isset($_POST['post_id']) ? (int) $_POST['post_id'] : 0;
        if (! $post_id) {
            wp_send_json_error(['message' => __('Invalid post ID.', 'publishpress-series-pro')]);
        }

        $defaults = PPS_Series_Meta_Box_Utilities::get_default_series_meta_box_data($post_id);
        update_post_meta($post_id, PPS_Series_Meta_Box_Utilities::META_PREFIX . 'layout_meta_value', $defaults);

        wp_send_json_success(['message' => __('Settings reset to defaults.', 'publishpress-series-pro')]);
    }
}
