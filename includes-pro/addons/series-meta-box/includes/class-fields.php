<?php
/**
 * Field definitions for Series Meta Box editor
 */

if (! defined('ABSPATH')) {
    exit;
}

class PPS_Series_Meta_Box_Fields
{
    const DEFAULT_TAB = 'general';

    /**
     * Hook provider
     */
    public static function init()
    {
        add_filter('pps_series_meta_box_editor_tabs', [__CLASS__, 'get_tabs'], 10, 2);
        add_filter('pps_series_meta_box_fields', [__CLASS__, 'filter_fields'], 10, 2);
    }

    /**
     * Define tabs for editor UI
     */
    public static function get_tabs($tabs, $post)
    {
        $base_tabs = [
            'general' => [
                'label' => __('General', 'publishpress-series-pro'),
                'icon'  => 'dashicons-admin-generic',
            ],
            'layout' => [
                'label' => __('Layout', 'publishpress-series-pro'),
                'icon'  => 'dashicons-layout',
            ],
            'styling' => [
                'label' => __('Styling', 'publishpress-series-pro'),
                'icon'  => 'dashicons-art',
            ],
        ];

        if (! is_array($tabs)) {
            $tabs = [];
        }

        return array_merge($base_tabs, $tabs);
    }

    /**
     * Prepare field definitions and merge custom filters
     */
    public static function filter_fields($fields, $post)
    {
        if (! is_array($fields)) {
            $fields = [];
        }

        $fields = array_merge($fields, self::get_general_fields());
        $fields = array_merge($fields, self::get_layout_fields());
        $fields = array_merge($fields, self::get_styling_fields());

        return $fields;
    }

    /**
     * Ensure admin UI can request raw definitions
     */
    public static function get_fields($post)
    {
        $fields = apply_filters('pps_series_meta_box_fields', [], $post);

        return $fields;
    }

    /**
     * Default settings for meta boxes
     */
    public static function get_default_settings($post_id = 0)
    {
        return PPS_Series_Meta_Box_Utilities::get_default_series_meta_box_data($post_id);
    }

    /**
     * General tab fields
     */
    private static function get_general_fields()
    {
        return [
            'layout_settings_separator' => [
                'type'  => 'category_separator',
                'label' => __('Content Template', 'publishpress-series-pro'),
                'tab'   => 'general',
            ],
            'meta_template' => [
                'label'    => __('Meta Template', 'publishpress-series-pro'),
                'type'     => 'textarea',
                'tab'      => 'general',
                'rows'     => 6,
                'sanitize' => [ 'wp_kses_post', 'trim' ],
                'description' => __('Template used when displaying meta box together with full content. Tokens such as %series_title_linked% are supported.', 'publishpress-series-pro'),
            ],
            'meta_excerpt_template' => [
                'label'    => __('Excerpt Template', 'publishpress-series-pro'),
                'type'     => 'textarea',
                'tab'      => 'general',
                'rows'     => 6,
                'sanitize' => [ 'wp_kses_post', 'trim' ],
                'description' => __('Template used when post content is output via `the_excerpt`.', 'publishpress-series-pro'),
            ],
            'layout_variant' => [
                'label'    => __('Layout Variant', 'publishpress-series-pro'),
                'type'     => 'select',
                'tab'      => 'general',
                'options'  => [
                    'classic' => __('Classic', 'publishpress-series-pro'),
                    'minimal' => __('Minimal Banner', 'publishpress-series-pro'),
                    'card'    => __('Card Highlight', 'publishpress-series-pro'),
                    'compact' => __('Compact Inline', 'publishpress-series-pro'),
                ],
                'sanitize' => 'sanitize_text_field',
                'description' => __('Determines default styling preset. Adjust colors below to customise.', 'publishpress-series-pro'),
            ],
            'metabox_position' => [
                'label'    => __('Display Position', 'publishpress-series-pro'),
                'type'     => 'select',
                'tab'      => 'layout',
                'options'  => [
                    'default' => __('As in Template', 'organize-series'),
                    'top'     => __('Top', 'organize-series'),
                    'bottom'  => __('Bottom', 'organize-series'),
                ],
                'sanitize' => 'sanitize_text_field',
            ],
            'limit_to_single' => [
                'label'    => __('Limit to Single Posts', 'publishpress-series-pro'),
                'type'     => 'checkbox',
                'tab'      => 'layout',
                'sanitize' => 'absint',
                'description' => __('Enable to display meta box only on single post view, not on archives.', 'publishpress-series-pro'),
            ],
        ];
    }

    /**
     * Layout specific fields
     */
    private static function get_layout_fields()
    {
        return [
            'layout_separator' => [
                'type'  => 'category_separator',
                'label' => __('Spacing & Alignment', 'publishpress-series-pro'),
                'tab'   => 'layout',
            ],
            'padding_vertical' => [
                'label'    => __('Vertical Padding (px)', 'publishpress-series-pro'),
                'type'     => 'number',
                'tab'      => 'layout',
                'min'      => 0,
                'max'      => 100,
                'sanitize' => 'absint',
            ],
            'padding_horizontal' => [
                'label'    => __('Horizontal Padding (px)', 'publishpress-series-pro'),
                'type'     => 'number',
                'tab'      => 'layout',
                'min'      => 0,
                'max'      => 100,
                'sanitize' => 'absint',
            ],
            'border_width' => [
                'label'    => __('Border Width (px)', 'publishpress-series-pro'),
                'type'     => 'number',
                'tab'      => 'layout',
                'min'      => 0,
                'max'      => 10,
                'sanitize' => 'absint',
            ],
            'border_radius' => [
                'label'    => __('Border Radius (px)', 'publishpress-series-pro'),
                'type'     => 'number',
                'tab'      => 'layout',
                'min'      => 0,
                'max'      => 60,
                'sanitize' => 'absint',
            ],
        ];
    }

    /**
     * Styling fields
     */
    private static function get_styling_fields()
    {
        return [
            'styling_separator' => [
                'type'  => 'category_separator',
                'label' => __('Colors & Appearance', 'publishpress-series-pro'),
                'tab'   => 'styling',
            ],
            'background_color' => [
                'label'    => __('Background Color', 'publishpress-series-pro'),
                'type'     => 'color',
                'tab'      => 'styling',
                'sanitize' => 'sanitize_hex_color',
            ],
            'text_color' => [
                'label'    => __('Text Color', 'publishpress-series-pro'),
                'type'     => 'color',
                'tab'      => 'styling',
                'sanitize' => 'sanitize_hex_color',
            ],
            'link_color' => [
                'label'    => __('Link Color', 'publishpress-series-pro'),
                'type'     => 'color',
                'tab'      => 'styling',
                'sanitize' => 'sanitize_hex_color',
            ],
            'border_color' => [
                'label'    => __('Border Color', 'publishpress-series-pro'),
                'type'     => 'color',
                'tab'      => 'styling',
                'sanitize' => 'sanitize_hex_color',
            ],
            'custom_css' => [
                'label'    => __('Custom CSS', 'publishpress-series-pro'),
                'type'     => 'textarea',
                'tab'      => 'styling',
                'rows'     => 6,
                'sanitize' => 'sanitize_textarea_field',
                'description' => __('Optional CSS (without <style> tags) applied when this layout renders.', 'publishpress-series-pro'),
            ],
        ];
    }
}
