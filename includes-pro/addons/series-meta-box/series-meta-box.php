<?php
/**
 * Series Meta Box module bootstrap
 */

if (! defined('ABSPATH')) {
    exit;
}

require_once __DIR__ . '/includes/class-utilities.php';
require_once __DIR__ . '/includes/class-post-type.php';
require_once __DIR__ . '/includes/class-fields.php';
require_once __DIR__ . '/includes/class-preview.php';
require_once __DIR__ . '/includes/class-admin-ui.php';
require_once __DIR__ . '/includes/class-ajax.php';

if (! defined('PPS_SERIES_META_BOX_NONCE')) {
    define('PPS_SERIES_META_BOX_NONCE', 'series-meta-box-editor');
}

if (! defined('PPS_SERIES_META_BOX_NONCE_FIELD')) {
    define('PPS_SERIES_META_BOX_NONCE_FIELD', 'series-meta-box-editor-nonce');
}

class PPS_Series_Meta_Box
{
    /**
     * Construct
     */
    public function __construct()
    {
        $this->init();
    }

    /**
     * Init module components
     */
    public function init()
    {
        PPS_Series_Meta_Box_Post_Type::init();
        PPS_Series_Meta_Box_Admin_UI::init();
        PPS_Series_Meta_Box_Ajax::init();

        add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
        add_action('save_post_' . PPS_Series_Meta_Box_Utilities::POST_TYPE, [$this, 'save_meta_box_data']);
        add_action('init', [$this, 'create_default_meta_boxes'], 6);
    }

    /**
     * Register admin assets
     */
    public function enqueue_admin_assets($hook)
    {
        global $typenow, $pagenow, $post;

        if ($typenow !== PPS_Series_Meta_Box_Utilities::POST_TYPE) {
            return;
        }

        if (! in_array($pagenow, ['post.php', 'post-new.php'])) {
            return;
        }

        wp_enqueue_style('wp-color-picker');
        wp_enqueue_code_editor(['type' => 'text/css']);

        $assets_base = plugins_url('assets/', __FILE__);

        wp_enqueue_script(
            'pps-series-meta-box-editor',
            $assets_base . 'js/series-meta-box-editor.js',
            ['jquery', 'wp-color-picker', 'underscore', 'code-editor'],
            ORG_SERIES_VERSION,
            true
        );

        wp_localize_script(
            'pps-series-meta-box-editor',
            'ppsSeriesMetaBoxEditor',
            [
                'post_id' => $post ? $post->ID : 0,
                'nonce' => wp_create_nonce('series-meta-box-nonce'),
                'ajax_url' => admin_url('admin-ajax.php'),
            ]
        );

        wp_enqueue_style(
            'pps-series-meta-box-editor',
            $assets_base . 'css/series-meta-box-editor.css',
            [],
            ORG_SERIES_VERSION
        );
    }

    /**
     * Persist meta box settings
     */
    public function save_meta_box_data($post_id)
    {
        if (empty($_POST[PPS_SERIES_META_BOX_NONCE_FIELD]) || ! wp_verify_nonce(sanitize_key($_POST[PPS_SERIES_META_BOX_NONCE_FIELD]), PPS_SERIES_META_BOX_NONCE)) {
            return;
        }

        $post = get_post($post_id);
        $fields = apply_filters('pps_series_meta_box_fields', PPS_Series_Meta_Box_Fields::get_fields($post), $post);
        $excluded = ['template_action', 'import_action'];
        $meta = [];

        foreach ($fields as $key => $args) {
            if (in_array($key, $excluded, true)) {
                continue;
            }

            if (! isset($_POST[$key])) {
                continue;
            }

            $value = $_POST[$key];

            if (isset($args['sanitize'])) {
                if (is_array($args['sanitize'])) {
                    foreach ($args['sanitize'] as $sanitize_cb) {
                        $value = is_array($value) ? map_deep($value, $sanitize_cb) : call_user_func($sanitize_cb, $value);
                    }
                } else {
                    $sanitize_cb = $args['sanitize'];
                    $value = is_array($value) ? $value : call_user_func($sanitize_cb, $value);
                }
            } else {
                $value = is_array($value) ? $value : sanitize_text_field($value);
            }

            $meta[$key] = $value;
        }

        update_post_meta($post_id, PPS_Series_Meta_Box_Utilities::META_PREFIX . 'layout_meta_value', $meta);
    }

    /**
     * Create default meta boxes
     */
    public function create_default_meta_boxes()
    {
        if (PPS_Series_Meta_Box_Utilities::defaults_created()) {
            return;
        }

        $defaults = [
            [
                'title' => __('Default Series Meta Box', 'publishpress-series-pro'),
                'slug' => 'default-series-meta-box',
                'settings' => PPS_Series_Meta_Box_Utilities::get_default_series_meta_box_data(),
            ],
            [
                'title' => __('Minimal Banner Meta Box', 'publishpress-series-pro'),
                'slug' => 'minimal-banner-meta-box',
                'settings' => $this->get_minimal_banner_settings(),
            ],
            [
                'title' => __('Card Highlight Meta Box', 'publishpress-series-pro'),
                'slug' => 'card-highlight-meta-box',
                'settings' => $this->get_card_highlight_settings(),
            ],
            [
                'title' => __('Compact Inline Meta Box', 'publishpress-series-pro'),
                'slug' => 'compact-inline-meta-box',
                'settings' => $this->get_compact_inline_settings(),
            ],
        ];

        $created_ids = [];

        foreach ($defaults as $data) {
            $existing = get_page_by_path($data['slug'], OBJECT, PPS_Series_Meta_Box_Utilities::POST_TYPE);
            if ($existing) {
                $created_ids[] = $existing->ID;
                continue;
            }

            $post_id = wp_insert_post([
                'post_title'   => $data['title'],
                'post_name'    => $data['slug'],
                'post_type'    => PPS_Series_Meta_Box_Utilities::POST_TYPE,
                'post_status'  => 'publish',
            ]);

            if (is_wp_error($post_id)) {
                continue;
            }

            update_post_meta($post_id, PPS_Series_Meta_Box_Utilities::META_PREFIX . 'layout_meta_value', $data['settings']);
            $created_ids[] = $post_id;
        }

        if (! empty($created_ids)) {
            PPS_Series_Meta_Box_Utilities::set_defaults_marker([
                'default_id' => $created_ids[0],
                'created_ids' => $created_ids,
            ]);

            PPS_Series_Meta_Box_Utilities::ensure_default_selection($created_ids[0]);
        }
    }

    /**
     * Minimal banner preset
     */
    private function get_minimal_banner_settings()
    {
        $settings = PPS_Series_Meta_Box_Utilities::get_default_series_meta_box_data();
        $settings['layout_variant'] = 'minimal';
        $settings['background_color'] = '#f7f7f7';
        $settings['text_color'] = '#1d2327';
        $settings['link_color'] = '#1a5aff';
        $settings['border_color'] = '#dfdfdf';
        $settings['border_width'] = 0;
        $settings['padding_vertical'] = 12;
        $settings['padding_horizontal'] = 16;
        return $settings;
    }

    /**
     * Card highlight preset
     */
    private function get_card_highlight_settings()
    {
        $settings = PPS_Series_Meta_Box_Utilities::get_default_series_meta_box_data();
        $settings['layout_variant'] = 'card';
        $settings['background_color'] = '#1a5aff';
        $settings['text_color'] = '#ffffff';
        $settings['link_color'] = '#ffe08a';
        $settings['border_color'] = '#1046c9';
        $settings['border_width'] = 0;
        $settings['padding_vertical'] = 20;
        $settings['padding_horizontal'] = 24;
        $settings['custom_css'] = '.seriesmeta-card-layout a { color: #ffe08a; font-weight: 600; }';
        return $settings;
    }

    /**
     * Compact inline preset
     */
    private function get_compact_inline_settings()
    {
        $settings = PPS_Series_Meta_Box_Utilities::get_default_series_meta_box_data();
        $settings['layout_variant'] = 'compact';
        $settings['background_color'] = '#ffffff';
        $settings['text_color'] = '#1d2327';
        $settings['link_color'] = '#1a5aff';
        $settings['border_color'] = '#ccd6f6';
        $settings['border_width'] = 1;
        $settings['padding_vertical'] = 8;
        $settings['padding_horizontal'] = 12;
        $settings['custom_css'] = '.seriesmeta-compact-layout { display: inline-flex; align-items: center; gap: 8px; }';
        return $settings;
    }
}

new PPS_Series_Meta_Box();
