<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => 'dev-master',
        'version' => 'dev-master',
        'reference' => '5528d67bcba08addc0c689616d8ccba356bbf548',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '5528d67bcba08addc0c689616d8ccba356bbf548',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'alledia/edd-sl-plugin-updater' => array(
            'pretty_version' => 'v1.6.23',
            'version' => '1.6.23.0',
            'reference' => '38253d04ac8875a88f62a3ebdca8034473f77418',
            'type' => 'library',
            'install_path' => __DIR__ . '/../alledia/edd-sl-plugin-updater',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'publishpress/instance-protection' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => '4193f84a8a680bf4c0d40cc64976ddcadd92158a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../publishpress/instance-protection',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'publishpress/pimple-pimple' => array(
            'pretty_version' => '3.5.0.10',
            'version' => '3.5.0.10',
            'reference' => 'f2784f4b1ccaf195b373a46a74cf3742e7c9f826',
            'type' => 'library',
            'install_path' => __DIR__ . '/../publishpress/pimple-pimple',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'publishpress/psr-container' => array(
            'pretty_version' => '2.0.1.10',
            'version' => '2.0.1.10',
            'reference' => '4ccd2cb058e7b93e96186791ca25870a02e35c5d',
            'type' => 'library',
            'install_path' => __DIR__ . '/../publishpress/psr-container',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'publishpress/wordpress-banners' => array(
            'pretty_version' => 'v1.3.1',
            'version' => '1.3.1.0',
            'reference' => 'bcb5f9f00973d53e2db027f9a05c059f18743fae',
            'type' => 'library',
            'install_path' => __DIR__ . '/../publishpress/wordpress-banners',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'publishpress/wordpress-edd-license' => array(
            'pretty_version' => '3.0.2',
            'version' => '3.0.2.0',
            'reference' => '423716f5122ac6fd535522f87015b61c4f6ce582',
            'type' => 'library',
            'install_path' => __DIR__ . '/../publishpress/wordpress-edd-license',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'publishpress/wordpress-reviews' => array(
            'pretty_version' => 'v1.1.20',
            'version' => '1.1.20.0',
            'reference' => '6d0b687a66439721b0432ef1320fd818cd56309f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../publishpress/wordpress-reviews',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'publishpress/wordpress-version-notices' => array(
            'pretty_version' => '2.1.5',
            'version' => '2.1.5.0',
            'reference' => 'caf37ca4705f89b882c1e53d9e592939568df944',
            'type' => 'library',
            'install_path' => __DIR__ . '/../publishpress/wordpress-version-notices',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
